import re
import requests
import json

class SkuExtractor:
    """SKU提取功能模块"""
    
    def __init__(self):
        pass
    
    def extract_skus(self, html_content):
        """从HTML内容中提取SKU编号"""
        # 正则表达式模式，用于匹配HTML中的SKU标识
        pattern = r'<a class="pairProInfoSku productUrl"[^>]*>([A-Za-z0-9]+)</a>'
        
        # 查找所有匹配
        matches = re.findall(pattern, html_content)
        
        # 返回去重后的结果
        return list(set(matches))
    
    def extract_product_name(self, html_content):
        """从HTML内容中提取商品名称"""
        # 正则表达式模式，用于匹配商品名称
        pattern = r'<span class="white-space no-new-line3 productHead">(.*?)</span>'
        
        # 查找匹配
        match = re.search(pattern, html_content)
        if match:
            return match.group(1).strip()
        return None
        
    def format_skus_for_display(self, skus):
        """将SKU列表格式化为显示文本"""
        if not skus:
            return "未找到SKU信息"
            
        return "\n".join([
            f"找到 {len(skus)} 个SKU:",
            "─" * 30,
            *[f"{idx+1}. {sku}" for idx, sku in enumerate(skus)],
            "─" * 30
        ])
    
    def search_sku_product_info(self, sku, cookie, api_url):
        """根据SKU搜索商品信息"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Cookie': cookie,
                'Referer': 'https://www.dianxiaomi.com/',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            # 设置请求参数
            payload = {
                'sortName': '2',
                'pageNo': '1',
                'pageSize': '50',
                'searchType': '2',
                'searchValue': sku,
                'productSearchType': '1',
                'shopId': '-1',
                'dxmState': 'online',
                'site': '0',
                'fullCid': '',
                'sortValue': '2',
                'productType': '',
                'productStatus': 'ONLINE'
            }
            
            # 发送请求获取商品信息
            response = requests.post(api_url, headers=headers, data=payload, timeout=30)
            response.raise_for_status()
            
            # 解析JSON响应
            json_data = response.json()
            
            # 检查是否成功
            if json_data.get('code') != 0 or 'data' not in json_data:
                return None, f"查询SKU {sku} 返回错误: {json_data.get('msg', '未知错误')}"
            
            # 检查是否有商品数据
            product_list = json_data.get('data', {}).get('page', {}).get('list', [])
            if not product_list:
                return None, f"未找到SKU {sku} 的商品信息"
            
            # 获取第一个商品信息
            product_info = product_list[0]
            product_name = product_info.get('productName', '')
            
            if not product_name:
                return None, f"未找到SKU {sku} 的商品名称"
            
            # 获取商品图片URL
            thumb_url = None
            variations = product_info.get('variations', [])
            if variations:
                thumb_url = variations[0].get('thumbUrl')
            
            return {
                'sku': sku,
                'product_name': product_name,
                'thumb_url': thumb_url,
                'product_info': product_info
            }, None
            
        except requests.RequestException as e:
            return None, f"请求失败: {str(e)}"
        except Exception as e:
            return None, f"搜索过程出错: {str(e)}"
    
    def compare_skus(self, api_skus, local_skus):
        """对比API SKU和本地SKU"""
        api_set = set(api_skus)
        local_set = set(local_skus)
        
        missing_skus = api_set - local_set
        extra_skus = local_set - api_set
        common_skus = api_set & local_set
        
        return {
            'api_count': len(api_skus),
            'local_count': len(local_skus),
            'missing_count': len(missing_skus),
            'extra_count': len(extra_skus),
            'common_count': len(common_skus),
            'missing_skus': list(missing_skus),
            'extra_skus': list(extra_skus),
            'common_skus': list(common_skus)
        }
