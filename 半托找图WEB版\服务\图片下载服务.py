import os
import requests
from urllib.parse import quote
from PIL import Image
import io

class ImageDownloader:
    """图片下载功能模块"""
    
    def __init__(self):
        # 可配置的路径检查条件
        self.target_path_suffix = r"\导出图\已完成"
        self.enable_target_suffix = True
        self.ignore_filename_chars = False
        self.ignore_prefix_count = 20
        self.ignore_suffix_count = 50
    
    def format_size(self, bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 MB"
        mb_size = bytes_size / (1024 * 1024)
        if mb_size < 1024:
            return f"{mb_size:.2f} MB"
        return f"{mb_size / 1024:.2f} GB"
    
    def process_search_term(self, search_term):
        """处理搜索词，支持忽略前后字符"""
        # 如果不忽略前后字符，直接返回原始搜索词
        if not self.ignore_filename_chars:
            return search_term
            
        # 如果搜索词长度小于忽略的字符数，直接返回原始搜索词
        if len(search_term) <= self.ignore_prefix_count + self.ignore_suffix_count:
            return search_term
            
        # 截取中间部分
        middle_part = search_term[self.ignore_prefix_count:-self.ignore_suffix_count if self.ignore_suffix_count > 0 else None]
        return middle_part.strip()
    
    def should_download_file(self, file_path, file_name):
        """检查文件是否符合下载条件"""
        # 检查文件扩展名
        if not os.path.splitext(file_name)[1].lower() in ('.png', '.jpg', '.jpeg'):
            return False
            
        # 如果不启用目标路径后缀，则只检查文件扩展名
        if not self.enable_target_suffix:
            return True
            
        # 启用目标路径后缀时，检查路径是否符合要求
        if self.target_path_suffix:
            # 将文件路径转换为小写以进行不区分大小写的比较
            lower_file_path = file_path.lower()
            lower_suffix = self.target_path_suffix.lower()
            
            # 检查文件路径是否包含目标路径后缀
            if not lower_file_path.endswith(lower_suffix + '\\' + file_name.lower()) and \
               not lower_file_path.endswith(lower_suffix + '/' + file_name.lower()):
                return False
                
        return True
    
    def compare_filenames(self, filename, search_term):
        """比较文件名和搜索词，支持忽略前后字符"""
        # 如果不忽略前后字符，直接比较文件名是否包含搜索词（不区分大小写）
        if not self.ignore_filename_chars:
            return search_term.lower() in filename.lower()
            
        # 忽略前后字符的比较逻辑
        # 1. 获取文件名（不含扩展名）
        name_without_ext = os.path.splitext(filename)[0]
        
        # 2. 如果文件名长度小于忽略的字符数，直接进行模糊匹配
        if len(name_without_ext) <= self.ignore_prefix_count + self.ignore_suffix_count:
            # 对于太短的文件名，只要包含搜索词的一部分即可（取搜索词的前一半进行匹配）
            half_term_length = max(3, len(search_term) // 2)  # 至少取3个字符
            search_part = search_term[:half_term_length].lower()
            return search_part in name_without_ext.lower()
            
        # 3. 截取中间部分
        middle_part = name_without_ext[self.ignore_prefix_count:-self.ignore_suffix_count if self.ignore_suffix_count > 0 else None]
        
        # 4. 模糊匹配：只要中间部分包含搜索词的一部分，或搜索词包含中间部分，就算匹配成功
        search_term_lower = search_term.lower()
        middle_part_lower = middle_part.lower()
        
        # 检查中间部分是否包含搜索词
        if search_term_lower in middle_part_lower:
            return True
            
        # 检查搜索词是否包含中间部分
        if middle_part_lower in search_term_lower:
            return True
            
        # 检查搜索词和中间部分是否有足够的相似性（至少有3个连续字符匹配）
        min_match_length = min(3, min(len(search_term_lower), len(middle_part_lower)))
        
        # 检查搜索词中是否有任何连续的部分匹配中间部分
        for i in range(len(search_term_lower) - min_match_length + 1):
            search_part = search_term_lower[i:i+min_match_length]
            if search_part in middle_part_lower:
                return True
                
        # 检查中间部分是否有任何连续的部分匹配搜索词
        for i in range(len(middle_part_lower) - min_match_length + 1):
            middle_part = middle_part_lower[i:i+min_match_length]
            if middle_part in search_term_lower:
                return True
                
        # 如果以上条件都不满足，则不匹配
        return False
    
    def prepare_directory(self, base_dir):
        """准备下载目录"""
        os.makedirs(base_dir, exist_ok=True)
        return base_dir
    
    def download_image(self, url, save_path, headers=None, max_retries=3):
        """下载图片文件"""
        if headers is None:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Connection': 'keep-alive'
            }
        
        for attempt in range(max_retries):
            try:
                response = requests.get(url, headers=headers, stream=True, timeout=15)
                response.raise_for_status()
                
                if response.status_code == 200:
                    # 确保保存目录存在
                    os.makedirs(os.path.dirname(save_path), exist_ok=True)
                    
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(1024):
                            f.write(chunk)
                    return True
                    
            except Exception as e:
                if attempt == max_retries - 1:  # 最后一次尝试
                    print(f"下载失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                    return False
                else:
                    print(f"下载重试 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                    
        return False
    
    def create_thumbnail(self, image_path, size=(200, 200)):
        """创建缩略图"""
        try:
            with Image.open(image_path) as img:
                img.thumbnail(size, Image.LANCZOS)
                
                # 保存缩略图到内存
                img_io = io.BytesIO()
                img.save(img_io, 'JPEG', quality=85)
                img_io.seek(0)
                
                return img_io
        except Exception as e:
            print(f"创建缩略图失败: {str(e)}")
            return None
    
    def update_config(self, config):
        """更新下载器配置"""
        self.target_path_suffix = config.get('target_suffix', self.target_path_suffix)
        self.enable_target_suffix = config.get('enable_target_suffix', self.enable_target_suffix)
        self.ignore_filename_chars = config.get('ignore_filename_chars', self.ignore_filename_chars)
        self.ignore_prefix_count = config.get('ignore_prefix_count', self.ignore_prefix_count)
        self.ignore_suffix_count = config.get('ignore_suffix_count', self.ignore_suffix_count)
