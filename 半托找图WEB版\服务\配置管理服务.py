import configparser
import os

class ConfigManager:
    """配置文件管理类"""
    CONFIG_FILE = "配置.ini"
    
    def __init__(self):
        self.config_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), self.CONFIG_FILE)
    
    def load_config(self):
        """加载配置文件"""
        config = configparser.ConfigParser(interpolation=None)
        
        # 默认配置
        default_settings = {
            'API': {
                'url': 'https://www.dianxiaomi.com/package/list.htm?pageNo=1&pageSize=300&shopId=6833815&state=paid&authId=-1&country=&platform=&isSearch=0&startTime=&endTime=&orderField=order_pay_time&isVoided=0&isRemoved=0&ruleId=-1&sysRule=&applyType=&applyStatus=&printJh=-1&printMd=-1&commitPlatform=&productStatus=&jhComment=-1&storageId=0&history=&custom=-1&isOversea=-1&timeOut=0&refundStatus=0&forbiddenStatus=-1&forbiddenReason=0&behindTrack=-1',
                'cookie': '',
                'base_url': 'https://www.dianxiaomi.com',
                'sku_search_url': 'https://www.dianxiaomi.com/api/popTemuProduct/pageList.json',
                'referer': 'https://www.dianxiaomi.com/'
            },
            'SEARCH': {
                'base_path': r"E:\图片\原图",
                'target_suffix': r"\导出图\已完成",
                'enable_target_suffix': 'True',
                'ignore_filename_chars': 'False',
                'ignore_prefix_count': '20',
                'ignore_suffix_count': '50'
            },
            'EVERYTHING': {
                'base_url': 'http://localhost:8080',
                'image_url': 'http://127.0.0.1:8080',
                'search_path': r"E:\图片\原图",
                'target_suffix': r"\导出图\已完成"
            },
            'SHARED': {
                'folder': r"\\*************\hhr-图库\合伙人-半托出单图\亚克力摆件\丽生-亚克力摆件"
            },
            'OPTIONS': {
                'strict_search': 'True'
            },
            'HEADERS': {
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'accept_encoding': 'gzip, deflate, br',
                'accept_language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'connection': 'keep-alive'
            }
        }
        
        # 检查配置文件是否存在
        if os.path.exists(self.config_file):
            try:
                config.read(self.config_file, encoding='utf-8')
            except Exception as e:
                print(f"读取配置文件错误: {e}")
        
        # 确保所有默认配置都存在
        for section, options in default_settings.items():
            if not config.has_section(section):
                config.add_section(section)
            for key, value in options.items():
                if not config.has_option(section, key):
                    config[section][key] = value
        
        # 保存更新后的配置
        self.save_config(config)
        return config
    
    def save_config(self, config):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                config.write(f)
            return True
        except Exception as e:
            print(f"保存配置文件错误: {e}")
            return False
            
    def update_config(self, section, key, value):
        """更新单个配置项"""
        config = self.load_config()
        if not config.has_section(section):
            config.add_section(section)
        config[section][key] = value
        return self.save_config(config)
    
    def get_config_value(self, section, key, default=None):
        """获取配置值"""
        config = self.load_config()
        try:
            return config.get(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return default
    
    def get_config_boolean(self, section, key, default=False):
        """获取布尔配置值"""
        config = self.load_config()
        try:
            return config.getboolean(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default
    
    def get_config_int(self, section, key, default=0):
        """获取整数配置值"""
        config = self.load_config()
        try:
            return config.getint(section, key)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return default
